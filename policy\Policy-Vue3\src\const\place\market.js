// 零工市场管理表格配置
export function createMarketTableOption() {
    return Promise.resolve({
        // 表格列配置 - 使用 column 而不是 columns
        column: [
            {
                prop: 'marketId',
                label: '市场ID',
                width: 80,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'marketName',
                label: '市场名称',
                minWidth: 150,
                align: 'center',
                showOverflowTooltip: true,
                showColumn: true,
                search: true,
                type: 'input',
                placeholder: '请输入市场名称'
            },
            {
                prop: 'marketCode',
                label: '市场编码',
                width: 120,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'marketType',
                label: '市场类型',
                width: 100,
                align: 'center',
                slot: 'marketType',
                showColumn: true,
                search: true,
                type: 'select',
                placeholder: '请选择市场类型',
                dicData: [
                    { label: '综合市场', value: '综合市场' },
                    { label: '专业市场', value: '专业市场' },
                    { label: '临时市场', value: '临时市场' }
                ]
            },
            {
                prop: 'regionName',
                label: '区域',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'contactPerson',
                label: '联系人',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'contactPhone',
                label: '联系电话',
                width: 120,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'workerCapacity',
                label: '零工容纳量',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'currentWorkerCount',
                label: '当前零工数',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'dailyAvgDemand',
                label: '日均需求',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'managementFee',
                label: '管理费用',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'isFeatured',
                label: '是否推荐',
                width: 100,
                align: 'center',
                slot: 'isFeatured',
                showColumn: true
            },
            {
                prop: 'status',
                label: '状态',
                width: 80,
                align: 'center',
                slot: 'status',
                showColumn: true,
                search: true,
                type: 'select',
                placeholder: '请选择状态',
                dicData: [
                    { label: '正常', value: '0' },
                    { label: '停用', value: '1' }
                ]
            },
            {
                prop: 'viewCount',
                label: '浏览次数',
                width: 100,
                align: 'center',
                showColumn: true
            },
            {
                prop: 'createTime',
                label: '创建时间',
                width: 180,
                align: 'center',
                showColumn: true,
                formatter: (row) => {
                    return row.createTime ? row.createTime.substring(0, 10) : ''
                }
            }
        ],

    })
}
