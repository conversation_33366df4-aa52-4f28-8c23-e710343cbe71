.conAuto2 {
    width: 1400px;
    min-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

/* 安全区域 */
/* 第一部分 */
.bannerBox {
    height: 340px;
}

.bannerBox .bannerText {
    margin-top: 100px;
}
.bannerBox .hd{
    position: absolute;
    bottom: 0;
    margin-left: 50%;
}
.bannerBox .hd ul{
    text-align: center;
    display: block;
    margin: 0 auto;
}
.bannerBox .hd li{
    display: inline-block;
    width: 9px;
    height: 9px;
    overflow: hidden;
    margin-right: 5px;
    text-indent: -999px;
    cursor: pointer;
    background:#e2e2e2;
    border-radius: 50px;
}
.bannerBox .hd li.on{
    background: #0052d9;
}
/* banner 轮播 */
.bannerSlide{
    width: 100% !important;
    height: 340px;
    top: 0;
    left: 0;
    min-width: 1400px;
    position: absolute !important;
}
.bannerSlide img{
    display: block;
    width: 100% !important;
    height: 100%;
}
/* new banner */
.bannerNew{
    background: url(../image/banner_06_19.jpg) no-repeat center;
    height: 260px;
}
/* new banner end */
/* slide box */
.slideTxtBox {
    right: 50px;
    top: 30px;
    width: 560px;
    height: 220px;
    background-color: #fff;
    box-shadow: 0px 0px 10px rgba(2, 42, 93, .12);
    border-radius: 8px;
    padding: 30px;
}
.slideTxtBox .bd ul li{
    height: 233px;
}
.slideTxtBox .bd ul li .sliLiTop {
    padding-bottom: 4px;
    border-bottom: 1px dashed #d0def5;
}

.slideTxtBox .bd ul li .date {
    width: 82px;
    height: 82px;
    background: url(../image/index_date.png) no-repeat center center;
    text-align: center;
}

.slideTxtBox .bd ul li .date .top {
    font-size: 38px;
    font-weight: bold;
    color: #0052d9;
}

.slideTxtBox .bd ul li .date .bottom {
    font-size: 16px;
    color: #0052d9;
}

.slideTxtBox .bd ul li .title {
    max-width: 450px;
    margin-left: 20px;
    font-size: 24px;
    color: #333;
    line-height: 38px;
    margin-top: 3px;
}
.slideTxtBox .bd ul li .title:hover{
    color: #0052d9;
}

.slideTxtBox .bd ul li .mainText {
    color: #666;
    font-size: 16px;
    line-height: 24px;
    margin-top: 15px;
}

.slideTxtBox .hd {
    right: 30px;
    bottom: 22px;
}

.slideTxtBox .hd .pageState {
    color: #333;
    font-size: 14px;
}

.slideTxtBox .hd .pageState span {
    font-size: 18px;
    color: #0052d9;
    font-weight: bold;
}

.slideTxtBox .hd .prev {
    display: block;
    width: 30px;
    height: 30px;
    background: url(../image/prev.png) left center no-repeat;
    margin-right: 25px;
}

.slideTxtBox .hd .prev:hover {
    background: url(../image/prevOn.png) center center no-repeat !important;
}

.slideTxtBox .hd .next {
    display: block;
    width: 30px;
    height: 30px;
    background: url(../image/next.png) left center no-repeat;
    margin-left: 25px;
}

.slideTxtBox .hd .next:hover {
    background: url(../image/nextOn.png) left center no-repeat;
}

/* 创业场地 */
.cycdBox {
    padding-top: 40px;
}

.contentTitle {
    font-size: 30px;
    font-weight: bold;
    line-height: 32px;
    color: #333;
}

.contentTitle em {
    color: #0052d9;
    font-size: 30px;
    font-weight: bold;
}

.xg {
    background: url(../../policy/images/ps_boxTitleBg.png) no-repeat top left;
    height: 7px;
    margin-top: 10px;
}
.xg2 {
    background: url(../../activity/image/titleStyleLine2.png) no-repeat top left;
    height: 7px;
    margin-top: 10px;
}
.cycdBox .numout li {
    width: 181px;
    height: 113px;
    margin-right: 30px;
    padding-left: 146px;
}
.cycdBox .numout li.bg1{
    background: url(../image/index_num1.png) no-repeat center center;
    background-size: 100% 100%;
}
.cycdBox .numout li.bg2{
    background: url(../image/index_num2.png) no-repeat center center;
    background-size: 100% 100%;
}
.cycdBox .numout li.bg3{
    background: url(../image/index_num3.png) no-repeat center center;
    background-size: 100% 100%;
}
.cycdBox .numout li.bg4{
    background: url(../image/index_num4.png) no-repeat center center;
    background-size: 100% 100%;
    margin-right: 0;
}
.cycdBox .numout li .num {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 5px;
    margin-top: 19px;
    /* background: -webkit-linear-gradient(170deg, #13b0be, #0052d9); 
    background: linear-gradient(170deg, #13b0be, #0052d9); 
    -webkit-background-clip: text; 
    background-clip: text;
    -webkit-text-fill-color: transparent; 
    color: transparent;  */
}

.cycdBox .numout li .text {
    font-size: 16px;
    color: #333;
}

.cycdRkBox {
    width: 570px;
    height: 197px;
    background: url(../image/index_cydt.png) no-repeat center center;
    box-shadow: 0 0 10px #7fa0c7;
    border-radius: 5px;
}

.cycdRkBox a {
    width: 100px;
    height: 40px;
    line-height: 40px;
    padding-left: 20px;
    background: url(../image/index_btnBg.png) no-repeat center center;
    color: #0052d9;
    margin-top: 129px;
    margin-left: 48px;
}

.animationBtn:hover {
    animation: rotate 0.3s ease infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0);
    }

    20% {
        transform: rotate(-2deg);
    }

    60% {
        transform: rotate(0);
    }

    80% {
        transform: rotate(2deg);
    }

    100% {
        transform: rotate(0);
    }
}

/* 创业实体 */
.cystBox {
    width: 100%;
    min-width: 1400px;
    height: 250px;
    padding-top: 40px;
    background: url(../image/index3Bg.png) no-repeat center center;
    /* background-size: 100% 100%; */
}

.cystBox ul {
    width: 1450px;
    margin-top: 34px;
}

.cystBox ul li {
    width: 276px;
    height: 53px;
    background: url(../image/index3LiBg.png) no-repeat center center;
    background-size: 100% 100%;
    box-shadow: 0px 0px 10px rgba(20, 82, 161, .15);
    border-radius: 5px;
    padding: 35px 28px;
    text-indent: 30px;
    font-size: 18px;
    color: #666;
    line-height: 30px;
    padding-right: 140px;
    margin-right: 34px;
    overflow: hidden !important;
}
.cystBox ul li:hover{
    margin-top: -20px;
}
.cystBox ul li em {
    font-size: 22px;
    color: #0052d9;
}

/* cycdMainBox */
.cycdMainBox {
    height: 550px;
    width: 100%;
    min-width: 1400px;
    background: url(../image/new_index_pic.jpg) no-repeat top center;
}
.cycdMainBox .cycdList{
    width: 1450px;
}
.cycdMainBox .cycdList .tabs{
    background-image: -moz-linear-gradient( 0deg, rgb(255,96,0) 0%, rgb(255,138,0) 100%);
        background-image: -webkit-linear-gradient( 0deg, rgb(255,96,0) 0%, rgb(255,138,0) 100%);
        background-image: -ms-linear-gradient( 0deg, rgb(255,96,0) 0%, rgb(255,138,0) 100%);
        right: 0;
        top: 0;
        padding: 0 15px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        color: #fff;
        z-index: 202;
        border-radius: 0 8px 0 8px;
      
}
.cycdMainBox .cycdList li {
    width: 447px;
    height: 242px;
    margin-bottom: 30px;
    margin-right: 25px;
    box-shadow: 0px 0px 10px rgba(20, 82, 161, .15);
    border-radius: 5px;
    cursor: pointer;
    background: url(../image/zcdLi.png) no-repeat;
}
.cycdMainBox .cycdList li a{
    padding: 20px 20px 10px;
}
.cycdMainBox .cycdList li:hover {
    background: url(../image/index3LiBgHover.png) no-repeat center center;
    background-size: 100% 100%;
}

.cycdMainBox .cycdList li .title {
    font-size: 18px;
    color: #333;
    margin-bottom: 13px;
}

.cycdMainBox .cycdList li:hover .title {
    color: #0052d9;
}

.cycdMainBox .cycdList li .mainImg {
    width: 100%;
    height: 100%;
}
.cycdMainBox .cycdList li .imgA {
    width: 180px;
    height: 120px;
    border-radius: 5px;
    overflow: hidden;
}

/* .mainBox3_right ul li img:hover, */
.cycdMainBox .cycdList li .mainImg:hover {
    transform: scale(1.2);
}

.cycdMainBox .cycdList li .bq1,
.cycdMainBox .cycdList li .bq2 {
    padding: 0px 10px;
    /* width: 58px; */
    height: 30px;
    line-height: 30px;
    text-align: center;
    font-size: 14px;
    background-color: #eaf2ff;
    border-radius: 0 25px 25px 25px;
    margin-right: 10px;
    color: #0052d9;
    max-width: 81px;
}

.cycdMainBox .cycdList li .bq2 {
    background-color: #ffefe5;
    color: #ff6000;
    margin-right: 0;
}

.cycdMainBox .cycdList li .liText {
    color: #999;
    font-size: 16px;
    line-height: 30px;
}

.cycdMainBox .cycdList li .mainLeft {
    max-width: 214px;
}

.cycdMainBox .cycdList li .pos {
    padding-left: 29px;
    background: url(../image/listAddIcon.png) no-repeat left center;
    color: #999;
    font-size: 14px;
    margin-top: 24px;
}

.cycdMainBox .cycdList li .learnMore {
    width: 94px;
    height: 40px;
    line-height: 40px;
    padding-left: 16px;
    color: #fff;
    margin-top: 15px;
    background: url(../image/index3_learnMore.png) no-repeat left center;

}
.cc3c8c9{
    color: #c3c8c9;
}

/* rzlcBox */
.rzlcBox{
    width: 100%;
    min-width: 1400px;
    height: 229px;
    padding-top: 30px;
    background: url(../image/index4Bg.png) no-repeat center center;
}
.rzlcBox .ulDiv{
    width: 1450px;
}
.rzlcBox .ulDiv .liDiv{
    width: 188px;
    height: 46px;
    padding: 22px 21px;
    background: url(../image/index4liBg.png) no-repeat center center;
    margin-right: 40px;
    box-shadow: 0px 0px 10px rgba(142,170,208,.16);
}
.rzlcBox .ulDiv .liDiv:hover{
    margin-top: -20px;
}
.ml22{
    margin-left: 22px;
}
.rzlcBox .ulDiv .index4JtImg{
    margin-right: 35px;
    margin-top: 33px;
}
.rzlcBox .ulDiv .liDiv.mr0{
    margin-right: 0px;
}
/* cdxqBox */
.cdxqBox{
    height: 522px;
    background: #fff url(../image/new_index_pic2.png) no-repeat left bottom;
    padding-top: 30px;
}
.moreBtn {
    height: 30px;
    padding-right: 12px;
    border-radius: 30px;
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
}

.moreBtn span {
    line-height: 30px;
    padding-right: 20px;
    padding-left: 11px;
    background: url(../../policy/images/ps_boxMore.png) no-repeat right center;
}

.moreBtn:hover {
    animation: moreBtn 0.5s 1 forwards;
    -webkit-animation: moreBtn 0.5s 1 forwards;
    box-shadow: 0px 0px 10px 3px rgba(0, 77, 198, 0.35);
}

@keyframes moreBtn {
    from {
        background-position: 0;
    }

    to {
        background-position: 71px;
    }
}
.moreBtnLong {
    width: 102px;
    height: 40px;
    line-height: 40px;
    padding-left: 8px;
    color: #fff;
    margin-top: 15px;
    border-radius: 30px;
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
}
.moreBtnLong span {
    line-height: 30px;
    padding-right: 20px;
    padding-left: 11px;
    background: url(../../policy/images/ps_boxMore.png) no-repeat right center;
}
.moreBtnLong:hover {
    animation: moreBtnLong 0.5s 1 forwards;
    -webkit-animation: moreBtnLong 0.5s 1 forwards;
    box-shadow: 0px 0px 10px 3px rgba(0, 77, 198, 0.35);
}
@keyframes moreBtnLong {
    from {
        background-position: 0;
    }

    to {
        background-position: 110px;
    }
}
.cdxqBox ul li {
    width: 525px;
    height: 191px;
    background: url(../image/index5LiBg.png) no-repeat center center;
    cursor: pointer;
    margin-right: 19px;
    margin-bottom: 23px;
}
.cdxqBox ul li .tabs{
    display: block;
    width: 54px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    color: #fff;
    position: absolute;
    top: 20px;
    left: 0;
}
.cdxqBox ul li .tab1{
    background: url(../image/cdxq_icon3.png) no-repeat center;
}
.cdxqBox ul li .tab2{
    background: url(../image/cdxq_icon2.png) no-repeat center;
}
.cdxqBox ul li .tab3{
    background: url(../image/cdxq_icon1.png) no-repeat center;
}
.cdxqBox ul li a{
    padding: 0px 20px;
    height: 191px;
}
.cdxqBox ul li:hover{
    background: url(../image/index5LiBgHover.png) no-repeat center center;
}
.cdxqBox ul li .title{
    display: block;
    height: 60px;
    line-height: 60px;
    font-size: 20px;
    color: #333;
    width: 379px;
    padding-left: 44px;
}
.cdxqBox ul li .icons{
    margin-top: 18px;
}
.cdxqBox ul li:hover .icons{
    animation: zoomInOut 2s infinite;
}
@keyframes zoomInOut {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2); /* 放大1.5倍 */
    }
  }
.cdxqBox ul li:hover .title{
    color: #0052d9;
}

.cdxqBox ul li .mainText{
    margin-top: 15px !important;
    font-size: 16px !important;
    color: #666 !important;
    line-height: 22px !important;
    height: 44px;
    
}
.bt{
    border-top: 1px dashed #eee;
    margin-top: 15px;
}
.cdxqBox ul li .ljgd{
    width: 80px;
    height: 14px;
    background: url(../image/index5Ljxq.png) no-repeat center center;
    margin-top: 20px;
}
.cdxqBox .mainRight{
    width: 300px;
    height: 400px;
    background: url(../image/index5Right.png) no-repeat center center;

}
.cdxqBox .mainRight .fbNow{
    width: 134px;
    height: 40px;
    line-height: 40px;
    padding-left: 54px;
    background: url(../image/index5FbNow.png) no-repeat center center;
    color: #0052d9;
    margin-left: 63px;
    margin-top: 230px;
}
.cdxqBox .mainRight .fbNow:hover{
    box-shadow: 0px 0px 10px rgba(20,82,161,.36);
}
.w1090{
    width: 1090px;
}
.w1450{
    width: 1410px;
}
/* 园区动态 */
.yqdtOut{
    width: 800px;
}
.yqdtOut .yqdtMain{
    width: 766px;
    height: 116px;
    border: 1px solid #fff;
    border-radius: 8px;
    background-image: -moz-linear-gradient( 90deg, rgb(245,249,253) 0%, rgb(255,255,255) 100%);
    background-image: -webkit-linear-gradient( 90deg, rgb(245,249,253) 0%, rgb(255,255,255) 100%);
    background-image: -ms-linear-gradient( 90deg, rgb(245,249,253) 0%, rgb(255,255,255) 100%);
    box-shadow: 0px 0px 10px 0px rgba(0, 82, 217, 0.14);
    margin-top: 18px;
    padding: 8px 16px 0;
}
.yqdtOut ul li{
    border-bottom: 1px dashed #eee;
    line-height: 56px;
    height: 56px;
    background: url(../image/index_yqdtIcon.png) no-repeat 2px center;
    padding-left: 26px;
}
.yqdtOut ul li:nth-child(even){
    border-bottom: none;
}
.yqdtOut ul li a{
    float: left;
    width: 600px;
    font-size: 18px;
}
.yqdtOut ul li span{
    float: right;
    font-size: 16px;
    color: #999;
}
.yqdtOut ul li:hover{
    background-color: rgb(245, 249, 253);
}
.yqdtOut ul li:hover a{
    color: #0052d9;
}
/* 筛选 */
.selectBox .selectModule{
    /* position: relative; */
    width: 225px;
    height: 60px;
    line-height: 60px;
    border-radius: 5px;
    overflow: hidden;
    background: #fff url(../image/selectModule_arrowOff.png) no-repeat 207px;
    font-size: 16px;
    box-shadow: 0px 0px 10px 3px #deecfe;
}

.selectBox .selectModule .title{
    float: left;
    background-color: #f2f8fd;
    width: 88px;
    height: 60px;
    color: #333;
    font-style: italic;
    text-align: center;
}
.selectBox .selectModule.on .title{
    background:linear-gradient(to right, #1a65dd, #2081d4);
    color: #d9d9d9;
}
.selectBox .selectModule.on .name{
    background:url(../image/selectModule_arrowOn.png) no-repeat 119px , linear-gradient(to right, #0052d9, #13b0be);
    color: #fff;
}
.selectBox .selectModule .name{
    float: left;
    width: 127px;
    color: #999;
    padding-left: 10px;
    cursor: pointer;
}
.selectBox .selectModule .name p{
    width: 80%;
}
.selectBox .selectList{
    top: 76px;
    z-index: 2;
}
.selectBox .selectList .arrowIcon{
    background: url(../image/selectModule_ListIcon02.png) no-repeat center;
    width: 14px;
    height: 8px;
    position: absolute;
    top: -8px;
    left: 38px;
}
.selectBox .selectList ul{
    min-width: 205px;
    max-height: 250px;
    overflow-y: scroll;
    background-color: #fff;
    border-radius: 5px;
    /* border: 1px solid #ff6000; */
    padding: 10px;
    box-shadow: 0px 0px 5px 2px #f3f7fd;
}
.selectBox .selectList ul::-webkit-scrollbar {
	width: 8px;
}

.selectBox .selectList ul::-webkit-scrollbar-thumb {
	width: 8px;
	height: 50px;
	border-radius: 25px;
	background: #eaf2ff;
}
.selectBox .selectList ul li{
    height: 50px;
    line-height: 50px;
    padding-left: 86px;
    padding-right: 25px;
    border-radius: 5px;
    color: #333;
    font-size: 16px;
    white-space: nowrap;
    cursor: pointer;
}
.selectBox .selectList ul li:hover{
    background: url(../image/selectModule_ListIcon.png) no-repeat 24px,linear-gradient(to right, #0052d9, #13b0be);
    color: #fff;
}
.selectBox .selectList ul li.on{
    background: url(../image/selectModule_ListIcon.png) no-repeat 24px,linear-gradient(to right, #0052d9, #13b0be);
    color: #fff;
}
/*数字滚动插件的CSS可调整样式*/

.mt-number-animate {
    font-family: '微软雅黑';
    line-height: 30px;
    height: 30px;
    /*设置数字显示高度*/
    ;
    font-size: 28px;
    /*设置数字大小*/
    overflow: hidden;
    display: inline-block;
    position: relative;
    font-weight: bold;
    color: #0052d9;
}

.mt-number-animate .mt-number-animate-dot {
    width: 15px;
    /*设置分割符宽度*/
    line-height: 42px;
    float: left;
    text-align: center;
}

.mt-number-animate .mt-number-animate-dom {
    width: 17px;
    /*设置单个数字宽度*/
    text-align: center;
    float: left;
    position: relative;
    top: 0;
}

.mt-number-animate .mt-number-animate-dom .mt-number-animate-span {
    width: 100%;
    float: left;
    background: none;
}

.mt-number-animate .mt-number-animate-dom {
    margin-left: 1px;
}


/* numberRun end */
/* 园区招商需求 */
.qyzsBox{
    width: 100%;
    min-width: 1400px;
    height: 426px;
    background: url(../image/yqzs_bg.jpg) no-repeat top center;
    padding-top: 36px;
}
.qyzsBox ul{
    width: 1430px;
    margin-top: 25px;
}
.qyzsBox li{
    width: 288px;
    height: 306px;
    background: url(../image/yqzs_li.png) no-repeat;
    box-shadow: 0px 0px 10px rgba(20, 82, 161, .15);
    margin-right: 29px;
    cursor: pointer;
    padding: 0 20px;
}
.qyzsBox li:hover{
    background: url(../image/yqzs_lion.png) no-repeat;
}
.qyzsBox li .parkTypeName{
    display: block;
    padding: 0 15px;
    height: 30px;
    line-height: 30px;
    color: #fff;
    background-image: -moz-linear-gradient( 0deg, rgb(0,82,217) 0%, rgb(19,176,190) 100%);
    background-image: -webkit-linear-gradient( 0deg, rgb(0,82,217) 0%, rgb(19,176,190) 100%);
    background-image: -ms-linear-gradient( 0deg, rgb(0,82,217) 0%, rgb(19,176,190) 100%);
    border-radius: 8px 0 8px 0;
    top: 0;
    left: 0;
    max-width: 297px;
}
.qyzsBox li .title{
    margin-top: 44px;
    line-height: 30px;
    height: 60px;
    font-size: 20px;
    padding-bottom: 14px;
    border-bottom: 1px dashed #eee;
    margin-bottom: 12px;
}
.qyzsBox li .infop{
    line-height: 30px;
    height: 30px;
    font-size: 16px;
    color: #999;
}
.qyzsBox li .btns{
    display: block;
    width: 90px;
    height: 30px;
    line-height: 30px;
    padding-left: 10px;
    color: #0052d9;
    background: url(../image/yqzs_btn.png) no-repeat;
    margin: 23px auto 0;
}
.qyzsBox li:hover .btns{
    background: url(../image/yqzs_btnOn.png) no-repeat;
    color: #fff;
}