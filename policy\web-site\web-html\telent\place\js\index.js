// 公用模块html
headerBar()
footerBar()

$("#placePage").addClass("on")
function clearsx(){
    localStorage.removeItem('cdposition');
    localStorage.removeItem('cdtype');
    localStorage.removeItem('cdarea');
    localStorage.removeItem('cddirection');
    localStorage.removeItem('cdmoney');
    localStorage.removeItem('cdlevel');
}
clearsx()

// API基础配置
const API_BASE_URL = 'http://localhost:80/sux-admin';

// 零工市场和用工信息API调用函数
const laborAPI = {
    // 获取零工市场列表
    getMarketList: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/public/labor/markets`,
            method: 'GET',
            data: params
        });
    },

    // 获取推荐零工市场列表
    getFeaturedMarketList: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/public/labor/markets/featured`,
            method: 'GET',
            data: params
        });
    },

    // 获取活跃零工市场列表
    getActiveMarketList: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/public/labor/markets/active`,
            method: 'GET',
            data: params
        });
    },

    // 获取用工信息列表
    getEmploymentList: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/public/labor/employments`,
            method: 'GET',
            data: params
        });
    },

    // 获取推荐用工信息列表
    getFeaturedEmploymentList: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/public/labor/employments/featured`,
            method: 'GET',
            data: params
        });
    },

    // 获取已发布用工信息列表
    getPublishedEmploymentList: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/public/labor/employments/published`,
            method: 'GET',
            data: params
        });
    },

    // 获取零工市场统计信息
    getMarketStatistics: function() {
        return $.ajax({
            url: `${API_BASE_URL}/public/labor/statistics/markets`,
            method: 'GET'
        });
    },

    // 获取综合统计信息
    getOverviewStatistics: function() {
        return $.ajax({
            url: `${API_BASE_URL}/public/labor/statistics/overview`,
            method: 'GET'
        });
    },

    // 获取所有筛选选项
    getFilterOptions: function() {
        return $.ajax({
            url: `${API_BASE_URL}/public/labor/filter-options`,
            method: 'GET'
        });
    },

    // 根据关键词搜索零工市场
    searchMarkets: function(keyword) {
        return $.ajax({
            url: `${API_BASE_URL}/public/labor/markets/search`,
            method: 'GET',
            data: { keyword: keyword }
        });
    },

    // 根据关键词搜索用工信息
    searchEmployments: function(keyword) {
        return $.ajax({
            url: `${API_BASE_URL}/public/labor/employments/search`,
            method: 'GET',
            data: { keyword: keyword }
        });
    },

    // 快速匹配零工市场和用工信息
    quickMatch: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/public/labor/quick-match`,
            method: 'GET',
            data: params
        });
    }
};
var viewModel = {
    cdggList: ko.observableArray(),//公告列表
    cycdList: ko.observableArray(),//零工市场列表
    cdxqCount: ko.observable(),//用工信息总数
    cdxqList: ko.observableArray(),//用工信息列表
    bagnnerList: ko.observableArray(),

    // 零工市场筛选条件
    position: ko.observable(''),//场地位置：
        positionName: ko.observable('全部'),//场地位置：
        positionList: ko.observableArray(), //场地位置：

        type: ko.observable(''),//场地类型：
        typeName: ko.observable('全部'),//场地类型：
        typeList: ko.observableArray(),//场地类型：

        level: ko.observable(''),//场地等级
        levelName: ko.observable('全部'),//场地等级
        levelList: ko.observableArray(),

        area: ko.observable(''),//场地面积：
        areaName: ko.observable('全部'),//场地面积：
        areaList01: ko.observableArray(),

        money: ko.observable(''),//收费类型：
        moneyName: ko.observable('全部'),//收费类型：
        moneyList: ko.observableArray(),

        direction: ko.observable(''),//行业方向
        directionName: ko.observable('全部'),//行业方向
        directionList: ko.observableArray(),

    // 用工信息筛选条件
    employmentType: ko.observable(''),//用工类型
    employmentTypeName: ko.observable('全部'),//用工类型名称
    employmentTypeList: ko.observableArray(),//用工类型列表

    workCategory: ko.observable(''),//工作类别
    workCategoryName: ko.observable('全部'),//工作类别名称
    workCategoryList: ko.observableArray(),//工作类别列表

    salaryType: ko.observable(''),//薪资类型
    salaryTypeName: ko.observable('全部'),//薪资类型名称
    salaryTypeList: ko.observableArray(),//薪资类型列表

    employmentRegion: ko.observable(''),//用工信息区域
    employmentRegionName: ko.observable('全部'),//用工信息区域名称

    // 搜索关键词
    searchKeyword: ko.observable(''),//搜索关键词
    employmentSearchKeyword: ko.observable(''),//用工信息搜索关键词
        selectData: function (data, data2) { //零工市场筛选项
            changeSelectStyle(data,data2)
            switch (data) {
                case '0':
                    viewModel.position(data2.baseId)
                    localStorage.setItem('cdposition',data2.baseId)
                    viewModel.positionName(data2.baseName)
                    break;
                case '1':
                    viewModel.type(data2.baseId)
                    localStorage.setItem('cdtype',data2.baseId)
                    viewModel.typeName(data2.baseName)
                    initarea()
                    viewModel.area('')
                    viewModel.areaName('全部')
                    $('.module3').removeClass('on')
                    break;

                case '3':
                    viewModel.level(data2.baseId)
                    localStorage.setItem('cdlevel',data2.baseId)
                    viewModel.levelName(data2.baseName)
                    break;
                case '2':
                    viewModel.area(data2.baseId)
                    localStorage.setItem('cdarea',data2.baseId)
                    viewModel.areaName(data2.baseName)
                    break;
                case '4':
                    viewModel.direction(data2.baseId)
                    localStorage.setItem('cddirection',data2.baseId)
                    viewModel.directionName(data2.baseName)
                    break;
                case '5':
                    viewModel.money(data2.baseId)
                    localStorage.setItem('cdmoney',data2.baseId)
                    viewModel.moneyName(data2.baseName)
                    break;
            }

            getCycdList()
        },

        // 用工信息筛选函数
        selectEmploymentData: function (data, data2) {
            changeSelectStyle(data,data2)
            switch (data) {
                case '0':
                    viewModel.employmentType(data2.baseId)
                    viewModel.employmentTypeName(data2.baseName)
                    break;
                case '1':
                    viewModel.workCategory(data2.baseId)
                    viewModel.workCategoryName(data2.baseName)
                    break;
                case '2':
                    viewModel.salaryType(data2.baseId)
                    viewModel.salaryTypeName(data2.baseName)
                    break;
                case '3':
                    viewModel.employmentRegion(data2.baseId)
                    viewModel.employmentRegionName(data2.baseName)
                    break;
            }
            getCdxqList() // 重新加载用工信息列表
        },

        // 搜索功能
        searchMarkets: function() {
            var keyword = viewModel.searchKeyword().trim();
            console.log('搜索零工市场，关键词:', keyword);

            // 显示加载状态
            showLoadingState();

            if (keyword) {
                laborAPI.searchMarkets(keyword).done(function(response) {
                    console.log('搜索零工市场响应:', response);
                    if (response.code === 200) {
                        var markets = response.rows || response.data || [];
                        console.log('搜索到的零工市场数量:', markets.length);

                        if (markets.length > 0) {
                            // 数据格式转换
                            markets.forEach(function(market) {
                                market.parkName = market.marketName;
                                market.parkAddress = market.address;
                                market.address = market.address;
                                market.parkLevel = market.marketType;
                                market.parkType = '零工市场';
                                market.acreage = market.workerCapacity || 0;
                                market.rzCompanyCount = market.currentWorkerCount || 0;
                                market.baseId = market.marketId;
                                market.isOpenSettle = '0';
                                market.applyTimeStatus = 0;

                                // 调试信息
                                console.log('处理零工市场数据:', {
                                    marketId: market.marketId,
                                    baseId: market.baseId,
                                    marketName: market.marketName
                                });
                            });

                            // 保存搜索结果数据用于滑动
                            marketData = markets;
                            marketCurrentIndex = 0;

                            // 使用动态HTML渲染
                            renderLaborMarkets(markets);
                            viewModel.cycdList(markets);
                        } else {
                            console.log('搜索结果为空');
                            marketData = [];
                            marketCurrentIndex = 0;
                            showNoMarketData();
                            viewModel.cycdList([]);
                        }
                    } else {
                        console.warn('搜索API返回错误:', response.msg || response.message);
                        marketData = [];
                        marketCurrentIndex = 0;
                        showNoMarketData();
                        viewModel.cycdList([]);
                    }
                }).fail(function(xhr, status, error) {
                    console.error('搜索零工市场失败:', error, status, xhr);
                    marketData = [];
                    marketCurrentIndex = 0;
                    showNoMarketData();
                    viewModel.cycdList([]);
                });
            } else {
                console.log('关键词为空，重新加载全部数据');
                getCycdList(); // 重新加载全部数据
            }
        },

        // 搜索用工信息功能
        searchEmployments: function() {
            var keyword = viewModel.employmentSearchKeyword().trim();
            console.log('搜索用工信息，关键词:', keyword);

            // 显示加载状态
            showEmploymentLoadingState();

            if (keyword) {
                laborAPI.searchEmployments(keyword).done(function(response) {
                    console.log('搜索用工信息响应:', response);
                    if (response.code === 200) {
                        var employments = response.rows || response.data || [];
                        console.log('搜索到的用工信息数量:', employments.length);

                        if (employments.length > 0) {
                            // 数据格式转换
                            employments.forEach(function (employment) {
                                employment.demandIntroduct = employment.workDescription || employment.description || '';
                                employment.demandIntroduct = delHtmlTag(employment.demandIntroduct);
                                employment.demandTitle = employment.title || '招聘信息';
                                employment.demandType = employment.workCategory || employment.employmentType || '零工';
                                employment.salaryRange = employment.salaryMin && employment.salaryMax ?
                                    employment.salaryMin + '-' + employment.salaryMax + '元/' + (employment.salaryType || '天') :
                                    '面议';
                                employment.workLocation = employment.workLocation || employment.regionName || '';
                                employment.contactInfo = employment.contactPhone || employment.contactPerson || '';
                                employment.baseId = employment.employmentId;
                                employment.companyName = employment.companyName || '';
                                employment.positionsNeeded = employment.positionsNeeded || 0;
                                employment.positionsFilled = employment.positionsFilled || 0;
                                employment.urgencyLevel = employment.urgencyLevel || 'normal';

                                // 调试信息
                                console.log('处理用工信息数据:', {
                                    employmentId: employment.employmentId,
                                    baseId: employment.baseId,
                                    title: employment.title
                                });
                            });

                            // 保存搜索结果数据用于滑动
                            employmentData = employments;
                            employmentCurrentIndex = 0;

                            // 渲染用工信息
                            renderEmployments(employments);
                            viewModel.cdxqList(employments);
                            viewModel.cdxqCount(employments.length);
                        } else {
                            console.log('搜索用工信息结果为空');
                            employmentData = [];
                            employmentCurrentIndex = 0;
                            showNoEmploymentData();
                            viewModel.cdxqList([]);
                            viewModel.cdxqCount(0);
                        }
                    } else {
                        console.warn('搜索用工信息API返回错误:', response.msg || response.message);
                        employmentData = [];
                        employmentCurrentIndex = 0;
                        showNoEmploymentData();
                        viewModel.cdxqList([]);
                        viewModel.cdxqCount(0);
                    }
                }).fail(function(xhr, status, error) {
                    console.error('搜索用工信息失败:', error, status, xhr);
                    employmentData = [];
                    employmentCurrentIndex = 0;
                    showNoEmploymentData();
                    viewModel.cdxqList([]);
                    viewModel.cdxqCount(0);
                });
            } else {
                console.log('用工信息关键词为空，重新加载全部数据');
                getCdxqList(); // 重新加载全部数据
            }
        },
        parkNoticeList:ko.observableArray(),
        qyzsList:ko.observableArray(),//园区招商
        yqzsInfo:function(data){
            window.open('./attractDet.html?id='+data.baseId)
        }
}
// 移除不存在的viewModel1引用
// Object.assign(viewModel, viewModel1);
// 更多列表
function moreList(){
    if(viewModel.cycdList().length>0){
        window.location.href='./placeList.html?index=1'
    }else{
        clearsx()
        window.location.href='./placeList.html'
    }
}
function changeSelectStyle(index,data){
    $('.selectModule').eq(index).addClass('on');

    if(!data.baseId){
        $('.selectModule').eq(index).removeClass('on');
    }
}
$('.selectList').mouseleave(function(){
    $(this).hide()
})
$('.selectModule').click(function(){
    console.log('点击筛选模块:', $(this).find('.title').text());

    if($(this).hasClass('module3')){
        if(!viewModel.type()){
            $.jBox.tip("请先选择场地类型！");
            return false
        }
    }

    // 隐藏其他下拉列表
    $('.selectList').hide();

    // 显示当前点击的下拉列表
    var $currentList = $(this).siblings('.selectList');
    if ($currentList.length > 0) {
        $currentList.show();
        console.log('显示下拉列表，选项数量:', $currentList.find('li').length);
    } else {
        console.warn('未找到对应的下拉列表');
    }
})

// 初始化所有筛选选项数据
function initAllFilterOptions() {
    // 获取所有筛选选项
    laborAPI.getFilterOptions().done(function(response) {
        console.log('筛选选项API响应:', response);
        if (response.code === 200 && response.data) {
            var data = response.data;

            // 初始化区域列表（从零工市场选项中获取）
            if (data.marketOptions && data.marketOptions.regions) {
                var regions = data.marketOptions.regions.map(function(item) {
                    return {
                        baseName: typeof item === 'string' ? item : (item.regionName || item.name),
                        baseId: typeof item === 'string' ? item : (item.regionCode || item.code)
                    };
                });
                regions.unshift({
                    baseName: '全部',
                    baseId: ''
                });
                viewModel.positionList(regions);
                console.log('区域选项已初始化:', regions.length);
            }

            // 初始化市场类型（作为场地类型）
            if (data.marketOptions && data.marketOptions.marketTypes) {
                var types = data.marketOptions.marketTypes.map(function(item) {
                    return {
                        baseName: item,
                        baseId: item
                    };
                });
                types.unshift({
                    baseName: '全部',
                    baseId: ''
                });
                viewModel.typeList(types);
                console.log('市场类型选项已初始化:', types.length);
            }

            // 初始化服务类别（作为场地等级）
            if (data.marketOptions && data.marketOptions.serviceCategories) {
                var levels = data.marketOptions.serviceCategories.map(function(item) {
                    return {
                        baseName: item,
                        baseId: item
                    };
                });
                levels.unshift({
                    baseName: '全部',
                    baseId: ''
                });
                viewModel.levelList(levels);
                console.log('服务类别选项已初始化:', levels.length);
            }

            // 初始化工作类别（作为行业方向）
            if (data.employmentOptions && data.employmentOptions.workCategories) {
                var directions = data.employmentOptions.workCategories.map(function(item) {
                    return {
                        baseName: item,
                        baseId: item
                    };
                });
                directions.unshift({
                    baseName: '全部',
                    baseId: ''
                });
                viewModel.directionList(directions);
                console.log('工作类别选项已初始化:', directions.length);
            }

            // 初始化用工类型（作为收费类型）
            if (data.employmentOptions && data.employmentOptions.employmentTypes) {
                var modes = data.employmentOptions.employmentTypes.map(function(item) {
                    return {
                        baseName: item,
                        baseId: item
                    };
                });
                modes.unshift({
                    baseName: '全部',
                    baseId: ''
                });
                viewModel.moneyList(modes);

                // 同时初始化用工信息的筛选选项
                viewModel.employmentTypeList(modes);
                console.log('用工类型选项已初始化:', modes.length);
            }

            // 初始化工作类别选项
            if (data.employmentOptions && data.employmentOptions.workCategories) {
                var workCategories = data.employmentOptions.workCategories.map(function(item) {
                    return {
                        baseName: item,
                        baseId: item
                    };
                });
                workCategories.unshift({
                    baseName: '全部',
                    baseId: ''
                });
                viewModel.workCategoryList(workCategories);
                console.log('工作类别选项已初始化:', workCategories.length);
            }

            // 初始化薪资类型选项
            if (data.employmentOptions && data.employmentOptions.salaryTypes) {
                var salaryTypes = data.employmentOptions.salaryTypes.map(function(item) {
                    return {
                        baseName: item,
                        baseId: item
                    };
                });
                salaryTypes.unshift({
                    baseName: '全部',
                    baseId: ''
                });
                viewModel.salaryTypeList(salaryTypes);
                console.log('薪资类型选项已初始化:', salaryTypes.length);
            }
        } else {
            console.warn('筛选选项API返回数据格式异常:', response);
            initDefaultFilterOptions();
        }
    }).fail(function(xhr, status, error) {
        // 失败时使用默认选项
        console.error('获取筛选选项失败:', error, status, xhr);
        initDefaultFilterOptions();
    });
}

// 初始化默认筛选选项
function initDefaultFilterOptions() {
    // 默认区域选项
    viewModel.positionList([
        { baseName: '全部', baseId: '' },
        { baseName: '市南区', baseId: 'shinan' },
        { baseName: '市北区', baseId: 'shibei' },
        { baseName: '李沧区', baseId: 'licang' },
        { baseName: '崂山区', baseId: 'laoshan' }
    ]);

    // 默认类型选项
    viewModel.typeList([
        { baseName: '全部', baseId: '' },
        { baseName: '综合市场', baseId: 'comprehensive' },
        { baseName: '专业市场', baseId: 'professional' },
        { baseName: '临时市场', baseId: 'temporary' }
    ]);

    // 默认等级选项
    viewModel.levelList([
        { baseName: '全部', baseId: '' },
        { baseName: '一级', baseId: 'level1' },
        { baseName: '二级', baseId: 'level2' },
        { baseName: '三级', baseId: 'level3' }
    ]);

    // 默认行业方向选项
    viewModel.directionList([
        { baseName: '全部', baseId: '' },
        { baseName: '建筑装修', baseId: 'construction' },
        { baseName: '家政服务', baseId: 'housekeeping' },
        { baseName: '物流配送', baseId: 'logistics' },
        { baseName: '餐饮服务', baseId: 'catering' }
    ]);

    // 默认收费类型选项
    viewModel.moneyList([
        { baseName: '全部', baseId: '' },
        { baseName: '日结', baseId: 'daily' },
        { baseName: '周结', baseId: 'weekly' },
        { baseName: '月结', baseId: 'monthly' }
    ]);

    // 初始化用工信息的默认筛选选项
    viewModel.employmentTypeList([
        { baseName: '全部', baseId: '' },
        { baseName: '日结', baseId: 'daily' },
        { baseName: '周结', baseId: 'weekly' },
        { baseName: '月结', baseId: 'monthly' },
        { baseName: '计件', baseId: 'piece' }
    ]);

    viewModel.workCategoryList([
        { baseName: '全部', baseId: '' },
        { baseName: '服务员', baseId: 'waiter' },
        { baseName: '保洁', baseId: 'cleaner' },
        { baseName: '搬运工', baseId: 'porter' },
        { baseName: '销售', baseId: 'sales' },
        { baseName: '厨师助手', baseId: 'kitchen_helper' },
        { baseName: '快递员', baseId: 'courier' },
        { baseName: '保安', baseId: 'security' }
    ]);

    viewModel.salaryTypeList([
        { baseName: '全部', baseId: '' },
        { baseName: '小时', baseId: 'hourly' },
        { baseName: '日薪', baseId: 'daily' },
        { baseName: '月薪', baseId: 'monthly' },
        { baseName: '计件', baseId: 'piece' }
    ]);
}

// 初始化场地面积选项（保持原有逻辑）
function initarea() {
    // 这里可以根据场地类型动态加载面积范围
    var areaOptions = [
        { baseName: '全部', baseId: '' },
        { baseName: '1000平方米以下', baseId: '0-1000' },
        { baseName: '1000-5000平方米', baseId: '1000-5000' },
        { baseName: '5000-10000平方米', baseId: '5000-10000' },
        { baseName: '10000平方米以上', baseId: '10000-999999' }
    ];
    viewModel.areaList01(areaOptions);
}
// 初始化 场地面积
function initarea() {
ajaxgetDataFull('api-app/v1/sysDictData/selectListByTypeid?typeId='+viewModel.type())
if (getData.code == 0) {
    var arr = getData.obj
    arr.unshift({
        baseName: '全部',
        baseId: ''
    })
    viewModel.areaList01(arr);
}
}
// banner管理
function initBanner(){
    var obj={
        pageNum:1,
        pageSize:1,
        bannerModule:2
    }
    ajaxgetDataFull('api-qingdao/v1/sysBannerManager/getFrontList',obj)
    if(getData.code==0){
        if(getData.obj.content.length>0){
            viewModel.bagnnerList(getData.obj.content[0].pictureFileList)
            viewModel.bagnnerList.unshift({fullPath:'./image/index_banner.png'})
        }
    }
}
// initBanner()
function linkPage(e){
    if(e==1){
        window.location.href='placeList.html'
    }else{
        window.location.href='demandList.html'
    }
}
// 场地公告
// getCdGg();
// function getCdGg() {
//     ajaxgetData('api-app/v1/qctParkNotice/top10', '', function (res) {
//         if (res.code == 0) {
//             res.obj.forEach(function (ele) {
//                 var date = ele.baseCreateTime
//                 ele.baseCreateTime = date.slice(0, 7);
//                 ele.date = date.slice(8, 10);
//                 ele.parkNoticeDetail = delHtmlTag(ele.parkNoticeDetail);
//             });
//             viewModel.cdggList(res.obj)
//             $(".slideTxtBox").slide({ mainCell: ".bd ul", autoPlay: true, effect: "left", interTime: 5000 });
//         }
//     })
// }
// 滑动相关变量
var marketCurrentIndex = 0;
var employmentCurrentIndex = 0;
var marketData = [];
var employmentData = [];

// 零工市场列表
getCycdList();
function getCycdList() {
    var params = {
        pageSize: 20, // 获取更多数据用于滑动
        pageNum: 1
    };

    // 添加筛选条件 - 根据实体字段正确映射
    if (viewModel.level()) {
        // level对应服务类别，但这个字段在LaborMarketInfo中是serviceCategories（JSON格式）
        // 暂时不添加此筛选条件，或者可以通过关键词搜索
    }
    if (viewModel.position()) {
        params.regionCode = viewModel.position();
    }
    if (viewModel.type()) {
        params.marketType = viewModel.type();
    }
    if (viewModel.direction()) {
        // direction对应工作类别，这个是EmploymentInfo的字段，不适用于零工市场
        // 可以通过关键词搜索或者忽略
    }
    if (viewModel.money()) {
        // money对应用工类型，这个也是EmploymentInfo的字段
        // 可以通过关键词搜索或者忽略
    }

    console.log('正在调用零工市场接口，参数：', params);

    // 显示加载状态
    showLoadingState();

    laborAPI.getMarketList(params).done(function(response) {
        console.log('零工市场接口响应：', response);

        if (response.code === 200) {
            var markets = response.rows || response.data || [];
            console.log('获取到的零工市场数据：', markets);

            if (markets.length > 0) {
                $(".nodataPicCycd").hide(); // 暂无数据-隐藏
            } else {
                $(".nodataPicCycd").show(); // 暂无数据-显示
            }

            // 数据格式转换，适配现有模板 - 根据LaborMarketInfo实体字段
            markets.forEach(function(market) {
                market.parkName = market.marketName;
                market.parkAddress = market.address;
                market.address = market.address; // 保持原字段
                market.parkLevel = market.marketType; // 使用市场类型作为等级显示
                market.parkType = '零工市场';
                market.acreage = market.workerCapacity || 0; // 可使用面积显示为容纳量
                market.rzCompanyCount = market.currentWorkerCount || 0; // 已入驻企业显示为当前工人数
                market.parkArea = market.workerCapacity ? market.workerCapacity + '人' : '待定';
                market.usableArea = market.currentWorkerCount ? market.currentWorkerCount + '人' : '0人';
                market.companyCount = market.currentWorkerCount || 0;
                market.availablePositions = market.workerCapacity || 0;
                market.occupiedPositions = market.currentWorkerCount || 0;
                market.rentPriceMin = market.managementFee || 0;
                market.rentPriceMax = market.serviceFeeRate || 0;
                market.industryDirection = market.serviceCategories || '综合服务';
                market.operationMode = market.operatingHours || '日常运营';
                market.contactPerson = market.contactPerson;
                market.contactPhone = market.contactPhone;
                market.description = market.description;
                market.imageUrl = market.imageUrl || './image/place_default.jpg';
                market.baseId = market.marketId;
                // 招商时间相关字段
                market.isOpenSettle = '0'; // 设置为开放状态
                market.applyTimeStatus = 0; // 长期招商
                market.applyStartDate = null;
                market.applyEndDate = null;

                // 调试信息
                console.log('处理零工市场数据:', {
                    marketId: market.marketId,
                    baseId: market.baseId,
                    marketName: market.marketName
                });
            });

            // 保存数据用于滑动
            marketData = markets;
            marketCurrentIndex = 0;

            // 使用动态HTML生成方式渲染数据
            renderLaborMarkets(markets);
            viewModel.cycdList(markets);
            console.log('零工市场数据已设置到viewModel.cycdList');
        } else {
            console.log('接口返回错误，code:', response.code, 'msg:', response.msg);
            showNoMarketData();
            viewModel.cycdList([]);
        }
    }).fail(function(xhr, status, error) {
        console.error('零工市场接口调用失败：', error, status, xhr);
        marketData = [];
        marketCurrentIndex = 0;
        showNoMarketData();
        viewModel.cycdList([]);
    });
}
// 园区动态
getNoticeList();
function getNoticeList() {
    var obj = {
        pageSize: 2,
        pageNum: 1
    };
    ajaxgetData('api-qingdao/v1/qctParkInformation/front/getParkInformationList', obj, function (data) {
        viewModel.parkNoticeList(data.obj.content);
    });
}
// 初始化统计数据
initnum()
function initnum(){
    laborAPI.getOverviewStatistics().done(function(response) {
        if (response.code === 200) {
            var data = response.data;
            var marketStats = data.marketStatistics || {};

            // 零工市场总数
            $("#onerun01").numberAnimate({
                num: marketStats.totalMarkets || marketStats.total_markets || 0,
                speed: 2000
            });

            // 工人容纳总量
            $("#onerun02").numberAnimate({
                num: marketStats.totalWorkerCapacity || marketStats.total_worker_capacity || 0,
                speed: 2000
            });

            // 当前工人总数
            $("#onerun03").numberAnimate({
                num: marketStats.totalCurrentWorkers || marketStats.total_current_workers || 0,
                speed: 2000
            });

            // 日均用工需求
            $("#onerun04").numberAnimate({
                num: marketStats.totalDailyDemand || marketStats.total_daily_demand || 0,
                speed: 2000
            });
        }
    }).fail(function() {
        // 失败时尝试获取零工市场统计
        laborAPI.getMarketStatistics().done(function(response) {
            if (response.code === 200) {
                var stats = response.data;

                $("#onerun01").numberAnimate({
                    num: stats.totalMarkets || stats.total_markets || 0,
                    speed: 2000
                });
                $("#onerun02").numberAnimate({
                    num: stats.totalWorkerCapacity || stats.total_worker_capacity || 0,
                    speed: 2000
                });
                $("#onerun03").numberAnimate({
                    num: stats.totalCurrentWorkers || stats.total_current_workers || 0,
                    speed: 2000
                });
                $("#onerun04").numberAnimate({
                    num: stats.totalDailyDemand || stats.total_daily_demand || 0,
                    speed: 2000
                });
            }
        }).fail(function() {
            // 完全失败时显示默认值
            $("#onerun01").numberAnimate({ num: 0, speed: 2000 });
            $("#onerun02").numberAnimate({ num: 0, speed: 2000 });
            $("#onerun03").numberAnimate({ num: 0, speed: 2000 });
            $("#onerun04").numberAnimate({ num: 0, speed: 2000 });
        });
    });
}

// 用工信息
getCdxqList();
function getCdxqList() {
    var params = {
        pageSize: 20, // 获取更多数据用于滑动
        pageNum: 1
    };

    // 添加筛选条件
    if (viewModel.employmentType && viewModel.employmentType()) {
        params.employmentType = viewModel.employmentType();
    }
    if (viewModel.workCategory && viewModel.workCategory()) {
        params.workCategory = viewModel.workCategory();
    }
    if (viewModel.salaryType && viewModel.salaryType()) {
        params.salaryType = viewModel.salaryType();
    }
    if (viewModel.employmentRegion && viewModel.employmentRegion()) {
        params.regionCode = viewModel.employmentRegion();
    }

    // 显示加载状态
    showEmploymentLoadingState();

    laborAPI.getEmploymentList(params).done(function(response) {
        console.log('用工信息接口响应:', response);
        if (response.code === 200) {
            var employments = response.rows || response.data || [];


            viewModel.cdxqCount(response.total || employments.length);

            // 数据格式转换，适配现有模板 - 根据EmploymentInfo实体字段
            employments.forEach(function (employment) {
                employment.demandIntroduct = employment.workDescription || employment.description || '';
                employment.demandIntroduct = delHtmlTag(employment.demandIntroduct);
                employment.demandTitle = employment.title || '招聘信息';
                employment.demandType = employment.workCategory || employment.employmentType || '零工';
                employment.salaryRange = employment.salaryMin && employment.salaryMax ?
                    employment.salaryMin + '-' + employment.salaryMax + '元/' + (employment.salaryType || '天') :
                    '面议';
                employment.workLocation = employment.workLocation || employment.regionName || '';
                employment.contactInfo = employment.contactPhone || employment.contactPerson || '';
                employment.baseId = employment.employmentId;
                employment.companyName = employment.companyName || '';
                employment.positionsNeeded = employment.positionsNeeded || 0;
                employment.positionsFilled = employment.positionsFilled || 0;
                employment.urgencyLevel = employment.urgencyLevel || 'normal';

                // 调试信息
                console.log('处理用工信息数据:', {
                    employmentId: employment.employmentId,
                    baseId: employment.baseId,
                    title: employment.title
                });
            });

            // 保存数据用于滑动
            employmentData = employments;
            employmentCurrentIndex = 0;

            // 渲染用工信息
            renderEmployments(employments);
            viewModel.cdxqList(employments);
        }
    }).fail(function() {
        employmentData = [];
        employmentCurrentIndex = 0;
        showNoEmploymentData();
        viewModel.cdxqCount(0);
        viewModel.cdxqList([]);
    });
}
// 推荐用工信息
getQyzsList();
function getQyzsList() {
    var params = {
        limit: 4
    };

    laborAPI.getFeaturedEmploymentList(params).done(function(response) {
        if (response.code === 200) {
            var featuredEmployments = response.data || [];
            if (featuredEmployments.length > 0) {
                $(".nodataPicQyzs").hide();//暂无数据-隐藏
            } else {
                $(".nodataPicQyzs").show();//暂无数据-显示
            }

            // 数据格式转换，适配现有模板 - 根据EmploymentInfo实体字段
            featuredEmployments.forEach(function (employment) {
                employment.demandIntroduct = employment.workDescription || employment.description || '';
                employment.demandIntroduct = delHtmlTag(employment.demandIntroduct);
                employment.demandTitle = employment.title || '推荐岗位';
                employment.demandType = employment.workCategory || employment.employmentType || '零工';
                employment.salaryRange = employment.salaryMin && employment.salaryMax ?
                    employment.salaryMin + '-' + employment.salaryMax + '元/' + (employment.salaryType || '天') :
                    '面议';
                employment.workLocation = employment.workLocation || employment.regionName || '';
                employment.contactInfo = employment.contactPhone || employment.contactPerson || '';
                employment.baseId = employment.employmentId;
                employment.companyName = employment.companyName || '';
                employment.positionsNeeded = employment.positionsNeeded || 0;
                employment.positionsFilled = employment.positionsFilled || 0;
                employment.urgencyLevel = employment.urgencyLevel || 'normal';
                employment.isFeatured = true; // 标记为推荐
            });

            viewModel.qyzsList(featuredEmployments);
        }
    }).fail(function() {
        $(".nodataPicQyzs").show();//暂无数据-显示
        viewModel.qyzsList([]);
    });
}
// 申请入驻
function sqrzFun() {
    if (!checkLogin()) {
        $.jBox.tip("请先登录");
        // setTimeout(function () {
        //     //跳转到登录界面
        //     //sessionStorage.setItem('loginUrl', '../place/index.html');
        //     window.location.href = 'https://tysfrz.isdapp.shandong.gov.cn/jpaas-jis-sso-server/sso/entrance/auth-center?appMark=QDCHUANGYEYPT&userType=2';
        // }, 500);
    } else {
        if (viewModel.mine().userType ==0) {//个人类型账号
            $.jBox.tip("个人账号无法认证为场地服务商，请通过企业账号进行申请入驻操作！");
        }else{
            if (viewModel.mine().fieldService == '0' || viewModel.mine().fieldService == '2' || viewModel.mine().fieldService == '3') {//未认证为场地服务商
                window.open("../member/authSpace.html?type=3");//跳转认证页面
            } else if (viewModel.mine().fieldService == '1') {//已经认证为场地服务商
                $.jBox.tip("您已认证为场地服务商");
            }
        }
        
    }
}
// 发布需求
function fbxqNow() {
    if (!checkLogin()) {
        $.jBox.tip("请先登录");
        // setTimeout(function () {
        //     //跳转到登录界面
        //     //sessionStorage.setItem('loginUrl', '../place/index.html');
        //     window.location.href = 'https://tysfrz.isdapp.shandong.gov.cn/jpaas-jis-sso-server/sso/entrance/auth-center?appMark=QDCHUANGYEYPT&userType=2';
        // }, 500);
    } else {
        if (viewModel.mine().fieldService == '1') {
            $.jBox.tip("场地服务商无法发布场地需求！")
            return false;
        }
        window.open("../member/parkDemandList.html");//跳转我发布的场地需求页面
    }
}
// 初始化筛选选项
initAllFilterOptions();

ko.applyBindings(viewModel, document.getElementById("viewModelBox"));
$(".bannerBox").slide({
    titCell:".hd ul",
    mainCell:".bannerSlide",
    autoPlay:true,
    effect:"fold",
    interTime:5000,
    autoPage:true
});
$(".yqdtOut").slide({
    mainCell:"ul",
    autoPlay:true,
    effect:"top",
    interTime:5000,
    autoPage:true,
    vis:2,
    scroll:2
});

// 动态HTML生成函数（滑动方式）
function renderLaborMarkets(markets) {
    // 隐藏加载状态
    var loadingState = document.getElementById('loadingState');
    if (loadingState) loadingState.style.display = 'none';

    if (!markets || markets.length === 0) {
        showNoMarketData();
        return;
    }

    console.log('渲染零工市场数据，共', markets.length, '条');
    var marketSlider = document.getElementById('marketSlider');
    var marketSliderContainer = document.getElementById('marketSliderContainer');

    if (marketSlider && marketSliderContainer) {
        var html = '';
        markets.forEach(function(market) {
            html += generateMarketCardHTML(market);
        });
        marketSlider.innerHTML = html;
        marketSliderContainer.style.display = 'block';

        var noDataTip = document.getElementById('noDataTip');
        if (noDataTip) {
            noDataTip.style.display = 'none';
        }

        // 更新滑动按钮状态
        updateMarketSliderButtons();
    }

    // 隐藏knockout绑定的列表，显示动态生成的列表
    $('.cycdList').hide();
    $(".nodataPicCycd").hide();
}

function generateMarketCardHTML(market) {
    // 处理费用显示
    var feeText = '免费';
    if (market.managementFee && market.serviceFeeRate) {
        feeText = '￥' + market.managementFee + '-' + market.serviceFeeRate + '/天';
    } else if (market.managementFee && market.managementFee > 0) {
        feeText = '￥' + market.managementFee + '/天';
    } else if (market.serviceFeeRate && market.serviceFeeRate > 0) {
        feeText = '服务费率: ' + market.serviceFeeRate + '%';
    }

    // 参考talentSpecial.html的卡片生成方式
    var cardClass = market.isFeatured ? 'place-card-new featured' : 'place-card-new';
    var featuredBadge = market.isFeatured ? '<div class="featured-badge">推荐</div>' : '';

    // 确保字段不为空时才显示
    var marketName = market.marketName || market.parkName || '未命名市场';
    var marketType = market.marketType || market.parkType || '综合市场';
    var regionName = market.regionName || market.address || '未知区域';
    var workerCapacity = market.workerCapacity || market.acreage || 0;
    var currentWorkerCount = market.currentWorkerCount || market.rzCompanyCount || 0;
    var dailyAvgDemand = market.dailyAvgDemand || market.dailyDemand || 0;
    var address = market.address || market.parkAddress || '地址待完善';
    var viewCount = market.viewCount || market.visitCount || 0;

    return `
        <div class="${cardClass}" onclick="showMarketDetails('${market.marketId || market.baseId}')">
            ${featuredBadge}
            <!-- 卡片头部 -->
            <div class="card-header-new">
                <h3 class="place-title-new" title="${marketName}">${marketName}</h3>
                <div class="place-rent-new">${feeText}</div>
            </div>

            <!-- 标签 -->
            <div class="place-meta-tags-new">
                <span class="meta-tag-new type-tag-new">${marketType}</span>
                <span class="meta-tag-new level-tag-new">${regionName}</span>
            </div>

            <!-- 信息网格 -->
            <div class="info-grid-new">
                <div class="info-item-new">
                    <div class="info-icon-new">👥</div>
                    <div class="info-content-new">
                        <div class="info-label-new">容纳人数</div>
                        <div class="info-value-new">${workerCapacity}人</div>
                    </div>
                </div>
                <div class="info-item-new">
                    <div class="info-icon-new">📊</div>
                    <div class="info-content-new">
                        <div class="info-label-new">当前工人</div>
                        <div class="info-value-new">${currentWorkerCount}人</div>
                    </div>
                </div>
                <div class="info-item-new">
                    <div class="info-icon-new">⭐</div>
                    <div class="info-content-new">
                        <div class="info-label-new">日均需求</div>
                        <div class="info-value-new">${dailyAvgDemand}人</div>
                    </div>
                </div>
            </div>

            <!-- 卡片底部 -->
            <div class="card-footer-new">
                <div class="place-address-new" title="${address}">${address}</div>
                <div>
                    <span class="view-count-new">浏览 ${viewCount} 次</span>
                    <button class="detail-btn-new" onclick="event.stopPropagation(); showMarketDetails('${market.marketId || market.baseId}')">查看详情</button>
                </div>
            </div>
        </div>
    `;
}

function showNoMarketData() {
    // 隐藏加载状态
    var loadingState = document.getElementById('loadingState');
    if (loadingState) loadingState.style.display = 'none';

    var marketSliderContainer = document.getElementById('marketSliderContainer');
    var noDataTip = document.getElementById('noDataTip');
    if (marketSliderContainer) marketSliderContainer.style.display = 'none';
    if (noDataTip) noDataTip.style.display = 'block';
    $('.cycdList').hide();
    $(".nodataPicCycd").show();
}

function showMarketDetails(marketId) {
    console.log('查看零工市场详情，ID:', marketId);
    if (marketId) {
        window.open('./marketDetail.html?id=' + marketId);
    } else {
        console.error('市场ID为空，无法跳转详情页');
    }
}

// 显示加载状态
function showLoadingState() {
    var loadingState = document.getElementById('loadingState');
    var marketSliderContainer = document.getElementById('marketSliderContainer');
    var noDataTip = document.getElementById('noDataTip');

    if (loadingState) loadingState.style.display = 'block';
    if (marketSliderContainer) marketSliderContainer.style.display = 'none';
    if (noDataTip) noDataTip.style.display = 'none';
    $('.cycdList').hide();
    $(".nodataPicCycd").hide();
}

// 零工市场滑动功能
function slideMarkets(direction) {
    var slider = document.getElementById('marketSlider');
    if (!slider || marketData.length === 0) return;

    var cardWidth = 340; // 卡片宽度 + 间距
    var visibleCards = Math.floor(slider.parentElement.offsetWidth / cardWidth);
    var maxIndex = Math.max(0, marketData.length - visibleCards);

    if (direction === 'prev') {
        marketCurrentIndex = Math.max(0, marketCurrentIndex - 1);
    } else {
        marketCurrentIndex = Math.min(maxIndex, marketCurrentIndex + 1);
    }

    var translateX = -marketCurrentIndex * cardWidth;
    slider.style.transform = `translateX(${translateX}px)`;

    updateMarketSliderButtons();
}

// 更新零工市场滑动按钮状态
function updateMarketSliderButtons() {
    var prevBtn = document.getElementById('marketPrevBtn');
    var nextBtn = document.getElementById('marketNextBtn');
    var slider = document.getElementById('marketSlider');

    if (!slider || marketData.length === 0) return;

    var cardWidth = 340;
    var visibleCards = Math.floor(slider.parentElement.offsetWidth / cardWidth);
    var maxIndex = Math.max(0, marketData.length - visibleCards);

    if (prevBtn) {
        prevBtn.disabled = marketCurrentIndex <= 0;
    }
    if (nextBtn) {
        nextBtn.disabled = marketCurrentIndex >= maxIndex;
    }
}

// 用工信息滑动功能
function slideEmployments(direction) {
    var slider = document.getElementById('employmentSlider');
    if (!slider || employmentData.length === 0) return;

    var cardWidth = 340; // 卡片宽度 + 间距
    var visibleCards = Math.floor(slider.parentElement.offsetWidth / cardWidth);
    var maxIndex = Math.max(0, employmentData.length - visibleCards);

    if (direction === 'prev') {
        employmentCurrentIndex = Math.max(0, employmentCurrentIndex - 1);
    } else {
        employmentCurrentIndex = Math.min(maxIndex, employmentCurrentIndex + 1);
    }

    var translateX = -employmentCurrentIndex * cardWidth;
    slider.style.transform = `translateX(${translateX}px)`;

    updateEmploymentSliderButtons();
}

// 更新用工信息滑动按钮状态
function updateEmploymentSliderButtons() {
    var prevBtn = document.getElementById('employmentPrevBtn');
    var nextBtn = document.getElementById('employmentNextBtn');
    var slider = document.getElementById('employmentSlider');

    if (!slider || employmentData.length === 0) return;

    var cardWidth = 340;
    var visibleCards = Math.floor(slider.parentElement.offsetWidth / cardWidth);
    var maxIndex = Math.max(0, employmentData.length - visibleCards);

    if (prevBtn) {
        prevBtn.disabled = employmentCurrentIndex <= 0;
    }
    if (nextBtn) {
        nextBtn.disabled = employmentCurrentIndex >= maxIndex;
    }
}

// 渲染用工信息
function renderEmployments(employments) {
    var employmentLoadingState = document.getElementById('employmentLoadingState');
    if (employmentLoadingState) employmentLoadingState.style.display = 'none';

    if (!employments || employments.length === 0) {
        showNoEmploymentData();
        return;
    }

    console.log('渲染用工信息数据，共', employments.length, '条');
    var employmentSlider = document.getElementById('employmentSlider');
    var employmentSliderContainer = document.getElementById('employmentSliderContainer');

    if (employmentSlider && employmentSliderContainer) {
        var html = '';
        employments.forEach(function(employment) {
            html += generateEmploymentCardHTML(employment);
        });
        employmentSlider.innerHTML = html;
        employmentSliderContainer.style.display = 'block';

        var noDataTip = document.getElementById('employmentNoDataTip');
        if (noDataTip) {
            noDataTip.style.display = 'none';
        }

        // 更新滑动按钮状态
        updateEmploymentSliderButtons();
    }
}

// 生成用工信息卡片HTML
function generateEmploymentCardHTML(employment) {
    // 处理薪资显示
    var salaryText = employment.salaryRange || '面议';
    if (employment.salaryMin && employment.salaryMax) {
        salaryText = employment.salaryMin + '-' + employment.salaryMax + '元/' + (employment.salaryType || '天');
    } else if (employment.salaryMin) {
        salaryText = employment.salaryMin + '元起/' + (employment.salaryType || '天');
    }

    var cardClass = employment.isFeatured ? 'employment-card-new featured' : 'employment-card-new';
    var featuredBadge = employment.isFeatured ? '<div class="featured-badge">推荐</div>' : '';
    var urgencyClass = employment.urgencyLevel === 'urgent' ? 'urgent' : '';

    // 确保字段不为空时才显示
    var jobTitle = employment.demandTitle || employment.title || employment.jobTitle || '招聘信息';
    var employmentType = employment.demandType || employment.employmentType || '零工';
    var workCategory = employment.workCategory || '通用岗位';
    var workLocation = employment.workLocation || employment.regionName || '工作地点待定';
    var companyName = employment.companyName || employment.employer || '招聘企业';
    var positionsNeeded = employment.positionsNeeded || employment.recruitCount || 0;
    var contactInfo = employment.contactInfo || employment.contactPhone || employment.contactPerson || '联系方式待完善';
    var workDescription = employment.demandIntroduct || employment.workDescription || employment.description || '工作描述待完善';

    // 限制描述长度
    if (workDescription.length > 50) {
        workDescription = workDescription.substring(0, 50) + '...';
    }

    return `
        <div class="${cardClass} ${urgencyClass}" onclick="showEmploymentDetails('${employment.employmentId || employment.baseId}')">
            ${featuredBadge}
            <!-- 卡片头部 -->
            <div class="card-header-new">
                <h3 class="employment-title-new" title="${jobTitle}">${jobTitle}</h3>
                <div class="employment-salary-new">${salaryText}</div>
            </div>

            <!-- 标签 -->
            <div class="employment-meta-tags-new">
                <span class="meta-tag-new employment-type-tag-new">${employmentType}</span>
                <span class="meta-tag-new employment-category-tag-new">${workCategory}</span>
                <span class="meta-tag-new employment-region-tag-new">${workLocation}</span>
            </div>

            <!-- 信息网格 -->
            <div class="info-grid-new">
                <div class="info-item-new">
                    <div class="info-icon-new">🏢</div>
                    <div class="info-content-new">
                        <div class="info-label-new">公司</div>
                        <div class="info-value-new" title="${companyName}">${companyName}</div>
                    </div>
                </div>
                <div class="info-item-new">
                    <div class="info-icon-new">👥</div>
                    <div class="info-content-new">
                        <div class="info-label-new">需求人数</div>
                        <div class="info-value-new">${positionsNeeded}人</div>
                    </div>
                </div>
                <div class="info-item-new">
                    <div class="info-icon-new">📞</div>
                    <div class="info-content-new">
                        <div class="info-label-new">联系方式</div>
                        <div class="info-value-new" title="${contactInfo}">${contactInfo}</div>
                    </div>
                </div>
            </div>

            <!-- 卡片底部 -->
            <div class="card-footer-new">
                <div class="employment-company-new" title="${employment.demandIntroduct || employment.workDescription || ''}">${workDescription}</div>
                <div>
                    <button class="employment-btn-new" onclick="event.stopPropagation(); showEmploymentDetails('${employment.employmentId || employment.baseId}')">立即申请</button>
                </div>
            </div>
        </div>
    `;
}

// 显示无用工信息数据
function showNoEmploymentData() {
    var employmentLoadingState = document.getElementById('employmentLoadingState');
    if (employmentLoadingState) employmentLoadingState.style.display = 'none';

    var employmentSliderContainer = document.getElementById('employmentSliderContainer');
    var noDataTip = document.getElementById('employmentNoDataTip');
    if (employmentSliderContainer) employmentSliderContainer.style.display = 'none';
    if (noDataTip) noDataTip.style.display = 'block';
    $(".nodataPicEmployment").show();
}

// 显示用工信息详情
function showEmploymentDetails(employmentId) {
    console.log('查看用工信息详情，ID:', employmentId);
    if (employmentId) {
        window.open('./employmentDetail.html?id=' + employmentId);
    } else {
        console.error('用工信息ID为空，无法跳转详情页');
    }
}

// 显示用工信息加载状态
function showEmploymentLoadingState() {
    var employmentLoadingState = document.getElementById('employmentLoadingState');
    var employmentSliderContainer = document.getElementById('employmentSliderContainer');
    var noDataTip = document.getElementById('employmentNoDataTip');

    if (employmentLoadingState) employmentLoadingState.style.display = 'block';
    if (employmentSliderContainer) employmentSliderContainer.style.display = 'none';
    if (noDataTip) noDataTip.style.display = 'none';
    $(".nodataPicEmployment").hide();
}

// HTML标签清理函数
function delHtmlTag(str) {
    if (!str) return '';
    return str.replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ').trim();
}

// 测试API连接
function testAPIConnection() {
    console.log('测试API连接...');

    // 测试零工市场API
    laborAPI.getMarketList({pageSize: 1, pageNum: 1}).done(function(response) {
        console.log('零工市场API测试成功:', response);
    }).fail(function(xhr, status, error) {
        console.error('零工市场API测试失败:', error, status, xhr);
    });

    // 测试用工信息API
    laborAPI.getEmploymentList({pageSize: 1, pageNum: 1}).done(function(response) {
        console.log('用工信息API测试成功:', response);
    }).fail(function(xhr, status, error) {
        console.error('用工信息API测试失败:', error, status, xhr);
    });
}

// 页面加载完成后测试API
$(document).ready(function() {
    setTimeout(testAPIConnection, 1000);
});

// 窗口大小改变时更新滑动按钮状态
window.addEventListener('resize', function() {
    updateMarketSliderButtons();
    updateEmploymentSliderButtons();
});

// 测试API连接
function testAPIConnection() {
    console.log('开始测试API连接...');

    // 测试零工市场API
    laborAPI.getMarketList({pageSize: 1, pageNum: 1}).done(function(response) {
        console.log('✅ 零工市场API测试成功:', response);
        if (response.code === 200) {
            console.log('零工市场数据示例:', response.rows ? response.rows[0] : response.data ? response.data[0] : '无数据');
        }
    }).fail(function(xhr, status, error) {
        console.error('❌ 零工市场API测试失败:', error, status, xhr.responseText);
    });

    // 测试用工信息API
    laborAPI.getEmploymentList({pageSize: 1, pageNum: 1}).done(function(response) {
        console.log('✅ 用工信息API测试成功:', response);
        if (response.code === 200) {
            console.log('用工信息数据示例:', response.rows ? response.rows[0] : response.data ? response.data[0] : '无数据');
        }
    }).fail(function(xhr, status, error) {
        console.error('❌ 用工信息API测试失败:', error, status, xhr.responseText);
    });

    // 测试筛选选项API
    laborAPI.getFilterOptions().done(function(response) {
        console.log('✅ 筛选选项API测试成功:', response);
        if (response.code === 200 && response.data) {
            console.log('筛选选项数据结构:', Object.keys(response.data));
        }
    }).fail(function(xhr, status, error) {
        console.error('❌ 筛选选项API测试失败:', error, status, xhr.responseText);
    });

    // 测试统计数据API
    laborAPI.getOverviewStatistics().done(function(response) {
        console.log('✅ 统计数据API测试成功:', response);
    }).fail(function(xhr, status, error) {
        console.error('❌ 统计数据API测试失败:', error, status, xhr.responseText);
    });
}
