package com.sux.web.controller.place;

import com.sux.common.annotation.Anonymous;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.system.domain.LaborMarketInfo;
import com.sux.system.domain.EmploymentInfo;
import com.sux.system.service.ILaborMarketInfoService;
import com.sux.system.service.IEmploymentInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 零工市场和用工信息公开API Controller（无需登录）
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Anonymous
@RestController
@RequestMapping("/public/labor")
public class PublicLaborMarketController extends BaseController {

    @Autowired
    private ILaborMarketInfoService laborMarketInfoService;

    @Autowired
    private IEmploymentInfoService employmentInfoService;

    /**
     * 查询零工市场信息列表（公开接口）
     */
    @GetMapping("/markets")
    public TableDataInfo getMarkets(LaborMarketInfo laborMarketInfo) {
        startPage();
        // 只查询活跃状态的零工市场
        laborMarketInfo.setStatus("0");
        List<LaborMarketInfo> list = laborMarketInfoService.selectLaborMarketInfoList(laborMarketInfo);
        return getDataTable(list);
    }

    /**
     * 查询推荐零工市场信息列表（公开接口）
     */
    @GetMapping("/markets/featured")
    public AjaxResult getFeaturedMarkets(@RequestParam(defaultValue = "10") Integer limit) {
        LaborMarketInfo laborMarketInfo = new LaborMarketInfo();
        laborMarketInfo.setStatus("0");
        laborMarketInfo.setIsFeatured(1);
        List<LaborMarketInfo> list = laborMarketInfoService.selectFeaturedLaborMarketInfoList(laborMarketInfo);
        // 限制返回数量
        if (list.size() > limit) {
            list = list.subList(0, limit);
        }
        return success(list);
    }

    /**
     * 查询活跃零工市场信息列表（公开接口）
     */
    @GetMapping("/markets/active")
    public AjaxResult getActiveMarkets(@RequestParam(defaultValue = "10") Integer limit) {
        LaborMarketInfo laborMarketInfo = new LaborMarketInfo();
        List<LaborMarketInfo> list = laborMarketInfoService.selectActiveLaborMarketInfoList(laborMarketInfo);
        // 限制返回数量
        if (list.size() > limit) {
            list = list.subList(0, limit);
        }
        return success(list);
    }

    /**
     * 根据关键词搜索零工市场信息（公开接口）
     */
    @GetMapping("/markets/search")
    public TableDataInfo searchMarkets(@RequestParam String keyword) {
        startPage();
        List<LaborMarketInfo> list = laborMarketInfoService.selectLaborMarketInfoByKeyword(keyword);
        // 过滤只返回活跃状态的市场
        list = list.stream()
                .filter(market -> "0".equals(market.getStatus()))
                .collect(java.util.stream.Collectors.toList());
        return getDataTable(list);
    }

    /**
     * 获取零工市场详细信息（公开接口）
     */
    @GetMapping("/markets/{marketId}")
    public AjaxResult getMarketDetail(@PathVariable("marketId") Long marketId) {
        LaborMarketInfo laborMarketInfo = laborMarketInfoService.selectLaborMarketInfoDetailByMarketId(marketId);
        if (laborMarketInfo != null && "0".equals(laborMarketInfo.getStatus())) {
            // 增加浏览次数
            laborMarketInfoService.updateLaborMarketInfoViewCount(marketId);
            return success(laborMarketInfo);
        }
        return error("零工市场信息不存在或已下线");
    }

    /**
     * 查询用工信息列表（公开接口）
     */
    @GetMapping("/employments")
    public TableDataInfo getEmployments(EmploymentInfo employmentInfo) {
        startPage();
        // 只查询已发布状态的用工信息
        employmentInfo.setStatus("published");
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoList(employmentInfo);
        return getDataTable(list);
    }

    /**
     * 查询推荐用工信息列表（公开接口）
     */
    @GetMapping("/employments/featured")
    public AjaxResult getFeaturedEmployments(@RequestParam(defaultValue = "10") Integer limit) {
        EmploymentInfo employmentInfo = new EmploymentInfo();
        employmentInfo.setStatus("published");
        employmentInfo.setIsFeatured(1);
        List<EmploymentInfo> list = employmentInfoService.selectFeaturedEmploymentInfoList(employmentInfo);
        // 限制返回数量
        if (list.size() > limit) {
            list = list.subList(0, limit);
        }
        return success(list);
    }

    /**
     * 查询已发布的用工信息列表（公开接口）
     */
    @GetMapping("/employments/published")
    public AjaxResult getPublishedEmployments(@RequestParam(defaultValue = "10") Integer limit) {
        EmploymentInfo employmentInfo = new EmploymentInfo();
        employmentInfo.setStatus("published");
        List<EmploymentInfo> list = employmentInfoService.selectPublishedEmploymentInfoList(employmentInfo);
        // 限制返回数量
        if (list.size() > limit) {
            list = list.subList(0, limit);
        }
        return success(list);
    }

    /**
     * 根据关键词搜索用工信息（公开接口）
     */
    @GetMapping("/employments/search")
    public TableDataInfo searchEmployments(@RequestParam String keyword) {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByKeyword(keyword);
        // 过滤只返回已发布的用工信息
        list = list.stream()
                .filter(employment -> "published".equals(employment.getStatus()))
                .collect(java.util.stream.Collectors.toList());
        return getDataTable(list);
    }

    /**
     * 获取用工信息详细信息（公开接口）
     */
    @GetMapping("/employments/{employmentId}")
    public AjaxResult getEmploymentDetail(@PathVariable("employmentId") Long employmentId) {
        EmploymentInfo employmentInfo = employmentInfoService.selectEmploymentInfoDetailByEmploymentId(employmentId);
        if (employmentInfo != null && "published".equals(employmentInfo.getStatus())) {
            // 增加浏览次数
            employmentInfoService.updateEmploymentInfoViewCount(employmentId);
            return success(employmentInfo);
        }
        return error("用工信息不存在或未发布");
    }

    /**
     * 根据核心字段搜索用工信息（公开接口）
     */
    @GetMapping("/employments/core-search")
    public TableDataInfo coreSearchEmployments(@RequestParam(required = false) String employmentType,
                                              @RequestParam(required = false) String workCategory,
                                              @RequestParam(required = false) String salaryType,
                                              @RequestParam(required = false) String regionCode,
                                              @RequestParam(required = false) String keyword) {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByCoreFields(
            employmentType, workCategory, salaryType, regionCode, keyword);
        // 过滤只返回已发布的用工信息
        list = list.stream()
                .filter(employment -> "published".equals(employment.getStatus()))
                .collect(java.util.stream.Collectors.toList());
        return getDataTable(list);
    }

    /**
     * 获取零工市场统计信息（公开接口）
     */
    @GetMapping("/statistics/markets")
    public AjaxResult getMarketStatistics() {
        Map<String, Object> statistics = laborMarketInfoService.selectLaborMarketInfoStatistics();
        return success(statistics);
    }

    /**
     * 获取综合统计信息（公开接口）
     */
    @GetMapping("/statistics/overview")
    public AjaxResult getOverviewStatistics() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 零工市场统计
            Map<String, Object> marketStats = laborMarketInfoService.selectLaborMarketInfoStatistics();
            result.put("marketStatistics", marketStats);
            
            // 用工信息统计 - 这里需要实现统计方法
            Map<String, Object> employmentStats = new HashMap<>();
            // 可以添加用工信息的统计逻辑
            result.put("employmentStatistics", employmentStats);
            
            return success(result);
        } catch (Exception e) {
            logger.error("获取综合统计信息失败", e);
            return error("获取统计信息失败");
        }
    }

    /**
     * 获取所有筛选选项（公开接口）
     */
    @GetMapping("/filter-options")
    public AjaxResult getFilterOptions() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 零工市场筛选选项
            Map<String, Object> marketOptions = new HashMap<>();
            marketOptions.put("marketTypes", laborMarketInfoService.selectAllMarketTypes());
            marketOptions.put("serviceCategories", laborMarketInfoService.selectAllServiceCategories());
            marketOptions.put("regions", laborMarketInfoService.selectAllRegions());
            result.put("marketOptions", marketOptions);

            // 用工信息筛选选项
            Map<String, Object> employmentOptions = new HashMap<>();
            employmentOptions.put("employmentTypes", employmentInfoService.selectAllEmploymentTypes());
            employmentOptions.put("workCategories", employmentInfoService.selectAllWorkCategories());
            employmentOptions.put("salaryTypes", employmentInfoService.selectAllSalaryTypes());
            employmentOptions.put("educationRequirements", employmentInfoService.selectAllEducationRequirements());
            employmentOptions.put("regions", employmentInfoService.selectAllRegions());
            result.put("employmentOptions", employmentOptions);

            return success(result);
        } catch (Exception e) {
            logger.error("获取筛选选项失败", e);
            return error("获取筛选选项失败");
        }
    }

    /**
     * 快速匹配接口 - 基于简单条件快速匹配零工市场和用工信息
     */
    @GetMapping("/quick-match")
    public AjaxResult quickMatch(@RequestParam(required = false) String marketType,
                                @RequestParam(required = false) String workCategory,
                                @RequestParam(required = false) String location,
                                @RequestParam(defaultValue = "5") Integer limit) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 匹配零工市场
            LaborMarketInfo marketSearch = new LaborMarketInfo();
            marketSearch.setStatus("active");
            if (marketType != null && !marketType.isEmpty()) {
                marketSearch.setMarketType(marketType);
            }
            if (location != null && !location.isEmpty()) {
                marketSearch.setRegionName(location);
            }

            List<LaborMarketInfo> matchedMarkets = laborMarketInfoService.selectLaborMarketInfoList(marketSearch);
            if (matchedMarkets.size() > limit) {
                matchedMarkets = matchedMarkets.subList(0, limit);
            }

            // 匹配用工信息
            EmploymentInfo employmentSearch = new EmploymentInfo();
            employmentSearch.setStatus("published");
            if (workCategory != null && !workCategory.isEmpty()) {
                employmentSearch.setWorkCategory(workCategory);
            }
            if (location != null && !location.isEmpty()) {
                employmentSearch.setRegionName(location);
            }

            List<EmploymentInfo> matchedEmployments = employmentInfoService.selectEmploymentInfoList(employmentSearch);
            if (matchedEmployments.size() > limit) {
                matchedEmployments = matchedEmployments.subList(0, limit);
            }

            result.put("markets", matchedMarkets);
            result.put("employments", matchedEmployments);
            result.put("searchCriteria", Map.of(
                "marketType", marketType != null ? marketType : "不限",
                "workCategory", workCategory != null ? workCategory : "不限",
                "location", location != null ? location : "不限"
            ));

            return success(result);
        } catch (Exception e) {
            logger.error("快速匹配失败", e);
            return error("快速匹配服务暂时不可用");
        }
    }
}
